using System.Collections.Generic;
using UnityEngine;

public class SoundManagerController : MonoBehaviour
{
    public AudioSource audioData;

    private Dictionary<Sounds, string> soundMap = new Dictionary<Sounds, string>
    {
        {Sounds.Click, "click-tap-computer-mouse-352734"},
        {Sounds.Checked, "pencil_check_mark_2-105940"},
        {Sounds.Started, "horn 2"},
        {Sounds.Win, "horn 2"},
        {Sounds.Next, "wolf-whistle-14621"},
        {Sounds.Wrong, "wrong-47985"},
    };
    public void Play(Sounds sound)
    {
        var clip = Resources.Load<AudioClip>("Sounds/" + soundMap[sound]);
        audioData.PlayOneShot(clip);
    }
}

public enum Sounds
{
    Click,
    Checked,
    Started,
    Win,
    Wrong,
    Next,
}
