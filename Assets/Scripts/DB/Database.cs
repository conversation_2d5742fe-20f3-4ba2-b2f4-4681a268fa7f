using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public static class Database
{
    private static string[] gameListStart = new string[]
    {
        "0-home",          "0-carrots",      "0-tap",           "1/33-1" /*26*/,
        "1/44-2" /*39*/,   "1/44-3" /*40*/,  "1/53-1" /*47*/,   "1/55-3" /*48*/,
        "1/55-2" /*48*/,   "1/45-3" /*54*/,  "1/44-6"/*55*/,    "1/45-1" /*98*/,
        "0-hole",          "1/h35-1" /*47*/, "1/h45-2" /*53*/, "1/h53-1" /*62*/,
        "1/h55-2" /*71*/,  "1/h45-1" /*90*/, "1/h54-1"/*112*/,  "1/h55-1" /*122*/,
        "0-2carrots",      "1/44-4" /*31*/, "1/44-5" /*36*/, "1/63-1" /*50*/,
        "1/45-2" /*116*/,  "1/55-5" /*130*/, "1/44-1" /*131*/,  "1/64-1" /*148*/,
        "1/74-1" /*175*/,  "1/55-1" /*238*/, "1/54-1" /*255*/,  "1/65-2" /*282*/,
        "1/h64-2" /*364*/, "1/h64-1" /*366*/, "1/55-7" /*398*/, "1/55-6" /*416*/,
        "0-gate",          "1/g46-1" /*61*/, "1/g54-1" /*62*/, "1/gh46-1" /*124*/,
        "1/g54-2" /*133*/, "1/g55-2" /*189*/, "1/g55-1" /*195*/, "1/g46-2" /*205*/,
        "1/g74-1" /*344*/, "1/h55-3" /*454*/, "1/47-1" /*474*/, "1/h47-1" /*577*/,
        "1/55-4" /*523*/, "1/g47-1" /*683*/, "1/g56-1" /*722*/, "1/74-2" /*753*/,
        "0-arrow",         "1/a34-1" /*34*/,  "1/a54-1" /*43*/, "1/a63-1"/*49*/,
        "1/a36-1"/*63*/,   "1/a45-1" /*64*/, "1/a55-5"/*78*/, "1/a54-2"/*149*/,
        "1/a46-1" /*208*/,"1/a55-1" /*220*/, "1/a64-2"/*295*/,"1/a64-1"/*312*/,
        "1/a55-6" /*337*/,"1/a55-3" /*381*/,"1/a55-2" /*428*/,"1/ah55-1" /*516*/,
    };

    private static string[] gameListTough = new string[]
    {
        "1/65-1" /*1062*/,   "1/a55-4" /*1070*/,   "1/g65-2"/*1171*/,   "1/ah55-3" /*1282*/,
        "1/ag47-1" /*1491*/, "1/a56-2" /*1492*/,   "1/h65-2"/*1644*/, "1/56-3" /*1705*/,   "1/56-1" /*1893*/,
        "1/66-1" /*2010*/,   "1/65-4"/*2092*/,     "1/h74-2" /*2107*/,  "1/h66-1" /*2437*/,
        "1/66-3"/*2485*/,    "1/g65-1" /*2577*/,   "1/ah55-2"/*2781*/,  "1/a65-2" /*2815*/,
        "1/g56-2"/*3323*/,   "1/56-2" /*3859*/,    "1/a56-3"/*4226*/, "1/g65-3"/*4313*/,  "1/66-5"/*4605*/,
        "1/75-1" /*4836*/,   "1/57-2" /*4989*/,  "1/a56-1" /*5115*/,  "1/66-6"/*5409*/,  "1/a65-1" /*5415*/,
        "1/ah74-1"/*6427*/,  "1/h74-1" /*6443*/,   "1/65-3" /*7861*/, "1/g66-4"/*9060*/,   "1/a75-1" /*9178*/,
        "1/a66-1" /*9682*/,  "1/a75-2"/*10159*/,   "1/66-2"/*15019*/,   "1/h65-1" /*15819*/,
        "1/57-1"/*20080*/,   "1/a66-2"/*30250*/,   "1/66-4"/*41760*/,"1/h75-1"/*99438*/
    };

    public static string[] GetList(LevelGroup group)
    {
        switch (group)
        {
            case LevelGroup.Tough:
                return gameListTough;
            case LevelGroup.Start:
            default:
                return gameListStart;
        }
    }

    public static (int, string) GetLevel(LevelGroup group, string level)
    {
        var gameList = GetList(group);
        if (string.IsNullOrEmpty(level))
        {
            var resolved = GameRecord.GetAllResolved(group).Select(r => r.Game).ToArray();
            return FindFirstUnresolved(gameList, resolved);
        }

        var index = Array.IndexOf(gameList.ToArray(), level);
        if (index < 0)
        {
            var resolved = GameRecord.GetAllResolved(group).Select(r => r.Game).ToArray();
            return FindFirstUnresolved(gameList, resolved);
        }
        return (index + 1, level);
    }

    public static (int, string) LoadNext(LevelGroup group, string currentLevel)
    {
        var gameList = GetList(group);
        var index = Array.IndexOf(gameList.ToArray(), currentLevel);
        if (index < gameList.Length - 1)
        {
            return (index + 2, gameList[index + 1]);
        }

        return (0, string.Empty);
    }

    private static (int, string) FindFirstUnresolved(string[] list, string[] resolved)
    {
        for (var i = 0; i < list.Length; i++)
        {
            var l = list[i];
            if (!resolved.Any(r => r == l))
            {
                return (i + 1, l);
            }
        }

        return (0, string.Empty);
    }

    public static GameDefinition LoadMap(string mapName)
    {
        switch (mapName)
        {
            // case "0-home":
            //     return HelpMaps.GetHelpHome();
            // case "0-carrots":
            //     return HelpMaps.GetHelpCarrots();
            // case "0-tap":
            //     return HelpMaps.GetHelpTap();
            // case "0-hole":
            //     return HelpMaps.GetHelpHole();
            // case "0-2carrots":
            //     return HelpMaps.GetHelp2Carrots();
            // case "0-gate":
            //     return HelpMaps.GetHelpGate();
            // case "0-arrow":
            //     return HelpMaps.GetHelpArrow();
            default:
                var json = Resources.Load<TextAsset>("Maps/" + mapName)?.text;
                // if (string.IsNullOrEmpty(json))
                // {
                //     return HelpMaps.GetHelpHome();
                // }
                var gameDef = Newtonsoft.Json.JsonConvert.DeserializeObject<GameDefinition>(json);
                // gameDef.Banned = gameDef.Banned?.Select(t => t - 1).ToList() ?? new List<int>();
                // gameDef.Locked = gameDef.Locked?.Select(l => l.Select(t => t - 1).ToList()).ToList() ?? new List<List<int>>();
                // gameDef.Answer = gameDef.Answer.Select(l => l.Select(t => t - 1).ToList()).ToList();
                // gameDef.Ends = gameDef.Ends.Select(l => l.Select(t => t - 1).ToList()).ToList();
                gameDef.Groups = gameDef.Groups.Select(g => new GroupDefinition
                {
                    Color = g.Color - 1,
                    Road = g.Road.Select(t => t - 1).ToList(),
                    Ends = g.Ends.Select(t => t - 1).ToList()
                }).ToList();

                return gameDef;
        }

    }
}

public enum LevelGroup
{
    Start,
    Tough,
}
