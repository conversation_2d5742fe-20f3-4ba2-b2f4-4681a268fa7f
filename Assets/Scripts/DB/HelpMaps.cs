using System;
using UnityEngine;

public static class HelpMaps
{

    private static GameObject CreateHand(MonoBehaviour board)
    {
        var hand = new GameObject("Hand");
        hand.transform.parent = board.transform;
        hand.transform.localScale = new Vector3(100f, 100f, 1f);
        hand.transform.localPosition = new Vector3(0, 0, 0);

        var handImage = new GameObject("Hand Image");
        handImage.transform.parent = hand.transform;
        handImage.transform.localPosition = new Vector3(0, 0, 0);
        handImage.transform.localRotation = Quaternion.Euler(0, 0, 120);
        handImage.transform.localScale = new Vector3(1.5f, 1.5f, 1f);

        var handSpriteRenderer = handImage.AddComponent<SpriteRenderer>();
        var handSprite = Resources.Load<Sprite>("Images/Board/Help/hand");
        handSpriteRenderer.color = new Color(0.3f, 0.3f, 0.3f, 1);
        handSpriteRenderer.sprite = handSprite;

        return hand;
    }

    public static Tween ShowHelp1Step1(BoardController board, GameObject hand)
    {
        hand.gameObject.SetActive(true);

        var start1 = new Vector3(-217, 210, 0);
        var end1 = new Vector3(283, 210, 0);

        var tween = new Tween(2.5f, (v) =>
        {
            hand.transform.localPosition = Vector3.Lerp(start1, end1, v);
        });

        board.StartCoroutine(tween.Loop());

        board.helpText.gameObject.SetActive(true);
        board.helpText.text = "Connect your first symbol!";

        return tween;
    }

    public static GameDefinition GetHelp1(GameDefinition gameDef)
    {
        GameObject hand = null;
        Tween tween = null;

        var steps = new bool[] { false, false, false };

        gameDef.OnGameStarted = (board) =>
        {
            hand = CreateHand(board);
            tween = ShowHelp1Step1(board, hand);
        };

        gameDef.OnDotManuallyAdded = (board) =>
        {
            hand.SetActive(false);
            tween.Pause();
        };

        gameDef.OnDotConnected = (board, dot) =>
        {
            board.helpText.gameObject.SetActive(false);
            switch (dot.Index)
            {
                case 2:
                    steps[0] = true;
                    break;
                case 4:
                    steps[1] = true;
                    break;
                case 5:
                    steps[2] = true;
                    break;
                default:
                    break;
            }

            var step = Array.FindIndex(steps, s => !s) + 1;
            switch (step)
            {
                case 1:
                    tween = ShowHelp1Step1(board, hand);
                    break;
                case 2:
                    hand.gameObject.SetActive(true);

                    var start2 = new Vector3(34, 210, 0);
                    var end2 = new Vector3(34, -44, 0);

                    tween = new Tween(1.5f, (v) =>
                    {
                        hand.transform.localPosition = Vector3.Lerp(start2, end2, v);
                    });

                    board.StartCoroutine(tween.Loop());

                    board.helpText.gameObject.SetActive(true);
                    board.helpText.text = "Now connect the '-' symbol";
                    break;
                case 3:
                    hand.gameObject.SetActive(true);

                    var start3 = new Vector3(-217f, -38f, 0);
                    var end31 = new Vector3(-217f, -287, 0);
                    var end32 = new Vector3(281, -287, 0);
                    var end33 = new Vector3(281, -38, 0);
                    var tweenSplit = new TweenSplit(1, 2, 1);
                    tween = new Tween(3f, (v) =>
                    {
                        var (value, index) = tweenSplit.Split(v);
                        switch (index)
                        {
                            case 0:
                                hand.transform.localPosition = Vector3.Lerp(start3, end31, value);
                                break;
                            case 1:
                                hand.transform.localPosition = Vector3.Lerp(end31, end32, value);
                                break;
                            case 2:
                                hand.transform.localPosition = Vector3.Lerp(end32, end33, value);
                                break;
                        }
                    });

                    board.StartCoroutine(tween.Loop());

                    board.helpText.gameObject.SetActive(true);
                    board.helpText.text = "Great! Connect all matching symbols to complete the level!";
                    break;
            }
        };

        return gameDef;
    }
}