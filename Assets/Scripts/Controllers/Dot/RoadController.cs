using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RoadController : MonoBehaviour
{
    public RoadDirection direction;
    public LineRenderer roadRenderer;
    public LineRenderer roadBackgroundRenderer;
    public float distance = 1;

    public void Start()
    {
        switch (direction)
        {
            case RoadDirection.Up:
                roadRenderer.SetPosition(1, new Vector3(0, distance, 0));
                roadBackgroundRenderer.SetPosition(1, new Vector3(0, distance, 0));
                break;
            case RoadDirection.Right:
                roadRenderer.SetPosition(1, new Vector3(distance, 0, 0));
                roadBackgroundRenderer.SetPosition(1, new Vector3(distance, 0, 0));
                break;
            case RoadDirection.Down:
                roadRenderer.SetPosition(1, new Vector3(0, -distance, 0));
                roadBackgroundRenderer.gameObject.SetActive(false);
                break;
            case RoadDirection.Left:
                roadRenderer.SetPosition(1, new Vector3(-distance, 0, 0));
                roadBackgroundRenderer.gameObject.SetActive(false);
                break;
        }
    }

    public void SetActive(bool active, int colorIndex = -1)
    {
        roadRenderer.gameObject.SetActive(active);
        if (colorIndex != -1)
        {
            roadRenderer.startColor = Config.COLORS[colorIndex];
            roadRenderer.endColor = Config.COLORS[colorIndex];
        }
    }

    public void Hide()
    {
        gameObject.SetActive(false);
    }
}



public enum RoadDirection
{
    Up = 1,
    Right = 2,
    Down = 4,
    Left = 8,
}
