using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public static class GameRecord
{
    public static List<LevelRecord> GetAllResolved(LevelGroup group)
    {
        var recordString = group == LevelGroup.Tough ? PlayerPrefs.GetString("Tough") : PlayerPrefs.GetString("Start");

        if (string.IsNullOrEmpty(recordString))
        {
            return new List<LevelRecord>();
        }

        var json = JsonUtility.FromJson<GameHistory>(recordString);
        var resolved = json?.games ?? Array.Empty<string>();

        return resolved.Select(r =>
        {
            var parts = r.Split(':');
            var isPerfect = parts.Length > 1 && parts[1] == "p"; // parts[0] is the level name
            return new LevelRecord
            {
                Game = parts[0],
                IsPerfect = isPerfect
            };
        }).ToList();
    }

    public static int SetGameEnded(LevelGroup group, string name, bool isPerfect)
    {
        var gamesRecords = GetAllResolved(group);
        var record = gamesRecords.FirstOrDefault(g => g.Game == name);
        if (record != null)
        {
            record.IsPerfect = record.IsPerfect || isPerfect;
            SaveGameHistory(group, gamesRecords);
            return gamesRecords.Count();
        }

        gamesRecords.Add(new LevelRecord { Game = name, IsPerfect = isPerfect });
        SaveGameHistory(group, gamesRecords);
        return gamesRecords.Count() + 1;
    }

    private static void SaveGameHistory(LevelGroup group, List<LevelRecord> gamesRecords)
    {
        var games = gamesRecords.Select(g =>
        {
            if (g.IsPerfect)
            {
                return $"{g.Game}:p";
            }
            return g.Game;
        }).ToArray();

        var history = new GameHistory { games = games };
        PlayerPrefs.SetString(group == LevelGroup.Tough ? "Tough" : "Start", JsonUtility.ToJson(history));
    }

    public static bool GetIsSoundMuted()
    {
        return PlayerPrefs.GetInt("SoundMuted") == 1;
    }

    public static void SetMuted(bool muted)
    {
        PlayerPrefs.SetInt("SoundMuted", muted ? 1 : 0);
    }

    public static bool GetIsMusicMuted()
    {
        return PlayerPrefs.GetInt("MusicMuted") == 1;
    }

    public static void SetMusicMuted(bool muted)
    {
        PlayerPrefs.SetInt("MusicMuted", muted ? 1 : 0);
    }

    public static int GetHintNumber()
    {
        return PlayerPrefs.GetInt("Hint");
    }

    public static void SetHintNumber(int num)
    {
        PlayerPrefs.SetInt("Hint", num);
    }

    public static string GetCurrentLevel(LevelGroup group)
    {
        return PlayerPrefs.GetString(group == LevelGroup.Tough ? "ToughCurrentLevel" : "StartCurrentLevel");
    }

    public static void SetCurrentLevel(LevelGroup group, string level)
    {
        PlayerPrefs.SetString(group == LevelGroup.Tough ? "ToughCurrentLevel" : "StartCurrentLevel", level);
    }
}

[Serializable]
public class GameHistory
{
    public string[] games;
}

public class LevelRecord
{
    public string Game { get; set; }
    public bool IsPerfect { get; set; }
}