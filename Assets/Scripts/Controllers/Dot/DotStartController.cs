
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class DotStartController : MonoBehaviour
{
    public Image progressImage;
    public BoardController board;
    public DotController cell;
    public int colorIndex = 0;
    public int count = 0;
    public int progress = 0;
    public TMP_Text countText;
    public SpriteRenderer startRenderer;

    public void Start()
    {
        startRenderer.color = Config.COLORS[colorIndex];
        progressImage.fillAmount = 0f;
        countText.text = $"{count}";
    }

    public void Select()
    {
        if (Config.IS_CHECKING_SOLUTION)
        {
            return;
        }

        transform.localPosition = new Vector3(0, 0, Config.OBJECT_Z - 0.2f);
        transform.localScale = new Vector3(1.1f, 1.1f, 1);
    }

    public void Unselect()
    {
        if (Config.IS_CHECKING_SOLUTION)
        {
            return;
        }

        transform.localPosition = new Vector3(0, 0, Config.OBJECT_Z);
        transform.localScale = new Vector3(1, 1, 1);
    }

    public void SetProgress(bool increase)
    {
        if (Config.IS_CHECKING_SOLUTION)
        {
            return;
        }

        if (increase)
        {
            progress++;
        }
        else
        {
            progress--;
        }
        // progressImage.fillAmount = (float)progress / count;
        // countText.text = $"{progress}/{count}";
    }
}
