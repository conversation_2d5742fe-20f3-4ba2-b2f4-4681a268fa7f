using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Diagnostics;
using NaughtyAttributes;
using TMPro;
using UnityEngine;
using Debug = UnityEngine.Debug;
using UnityEngine.UI;

public class BoardController : MonoBehaviour
{
    public int level;
    public string mapName;
    public LevelGroup levelGroup;
    public GameObject dotPrefab;
    public TMP_Text helpText;
    public Button hintButton;
    public Button resetButton;

    public BoardHeaderController header;
    public BoardFooterController footer;
    public GameRunnerController ctrl;
    public DotController[] Cells;

    public const float BOARD_WIDTH = 1000f;
    public float dotDist = 150f;

    private DotController selectedOrigin = null;
    private DotController currentCell = null;
    private DotController previousCell = null;

    public GameDefinition gameDef;
    public GameStatus status = GameStatus.Ready;


    IEnumerator Start()
    {
        gameDef = Database.LoadMap(mapName);

        yield return null;

        var rowNumber = gameDef.Size[0];
        var colNumber = gameDef.Size[1];

        var max = Mathf.Max(rowNumber, colNumber);

        switch (max)
        {
            case 3:
                dotDist = 250;
                break;
            case 4:
                dotDist = 220f;
                break;
            case 5:
                dotDist = 190f;
                break;
            case 6:
                dotDist = 160f;
                break;
            case 7:
                dotDist = 140f;
                break;
            case 8:
                dotDist = 130f;
                break;
        }

        var actualWidth = (colNumber - 1) * dotDist;
        var actualHeight = (rowNumber - 1) * dotDist;
        var startX = actualWidth / -2f;
        var startY = actualHeight / 2f;

        var hueGap = 1f / gameDef.Answer.Count;
        var hue = UnityEngine.Random.Range(0f, 1f);

        Cells = new DotController[colNumber * rowNumber];
        for (var i = 0; i < rowNumber; i++)
        {
            for (var j = 0; j < colNumber; j++)
            {
                var ctrl = Instantiate(dotPrefab, transform).GetComponent<DotController>();

                ctrl.transform.localPosition = new Vector3(j * dotDist + startX, -i * dotDist + startY, 0);
                ctrl.I = i;
                ctrl.J = j;

                var index = i * colNumber + j;
                ctrl.Index = index;

                Cells[index] = ctrl;
                ctrl.board = this;

                // if (gameDef.Banned?.Any(b => b == index) == true)
                // {
                //     ctrl.isBanned = true;
                //     ctrl.gameObject.SetActive(false);
                //     continue;
                // }

                var alive = gameDef.Answer.FirstOrDefault(a => a.First() == index);
                if (alive != null)
                {
                    ctrl.SetStart(alive, gameDef.Ends, hue, false);
                    hue += hueGap;
                    if (hue > 1)
                    {
                        hue -= 1;
                    }
                    continue;
                }

                var colorIndex = gameDef.Ends.FindIndex(e => e.Any(i => i == index));
                if (colorIndex >= 0)
                {
                    ctrl.SetEnd(colorIndex);
                    continue;
                }

                ctrl.cellType = DotType.Road;
            }
        }

        header.Init(this);
        footer.Init(this);

        PlaySound(Sounds.Started);

        yield return new WaitForSeconds(0.5f);

        status = GameStatus.Started;
        gameDef.OnGameStarted?.Invoke(this);
    }

    void Update()
    {
        HandleInput();
    }

    private void Unselect()
    {
        selectedOrigin?.Unselect();
        selectedOrigin = null;
        previousCell = null;
        currentCell = null;
    }

    private void HandleInput()
    {
        if (status != GameStatus.Started)
        {
            return;
        }

        if (selectedOrigin == null)
        {
            if (Input.GetMouseButtonDown(0))
            {
                var selectedCell = GetCell();
                if (selectedCell != null)
                {
                    if (selectedCell.isBanned)
                    {
                        return;
                    }

                    if (selectedCell.ConnectTo != null && !selectedCell.ConnectTo.isLocked)
                    {
                        selectedOrigin = selectedCell.ConnectTo;
                        selectedOrigin.Select();

                        previousCell = selectedCell;
                    }
                    else
                    {
                        selectedCell.ShowWrong();
                    }
                }
            }

            return;
        }

        if (Input.GetMouseButtonUp(0))
        {
            Unselect();
            return;
        }

        var cell = GetCell();
        if (cell == null || cell == previousCell || cell == currentCell || cell.isBanned)
        {
            return;
        }
        currentCell = cell;

        if (cell.ConnectTo == null)
        {
            if (selectedOrigin.Addable(cell, previousCell))
            {
                selectedOrigin.AddCell(cell, previousCell);
                previousCell = cell;
                gameDef.OnDotManuallyAdded?.Invoke(this);
                if (cell.cellType == DotType.End)
                {
                    gameDef.OnDotConnected?.Invoke(this, cell);
                    Unselect();
                    CheckGameEnded();
                }
            }
            else
            {
                Unselect();
                cell.ShowWrong();
            }
        }
        else
        {
            if (cell.ConnectTo.Index == selectedOrigin.Index)
            {
                if (previousCell?.Index != cell.Index && previousCell?.ConnectTo?.Index == selectedOrigin.Index && previousCell?.previousCell?.Index == cell.Index)
                {
                    selectedOrigin.DeleteCell(previousCell);
                }
                previousCell = cell;
            }

            else
            {
                // Replace
                if (selectedOrigin.Addable(cell, previousCell) && cell.ConnectTo.DeleteCell(cell))
                {
                    selectedOrigin.AddCell(cell, previousCell);
                    gameDef.OnDotManuallyAdded?.Invoke(this);
                    previousCell = cell;
                }
                else
                {
                    Unselect();
                    cell.ShowWrong();
                }
            }
        }
    }

    private DotController GetCell()
    {
        var mouseBoardPos = ctrl.GetMousePosition();
        return Cells.FirstOrDefault(n => !n.isBanned && Vector2.Distance(n.transform.localPosition, mouseBoardPos) < dotDist / 2);
    }

    public void PlaySound(Sounds sound)
    {
        if (Config.IS_CHECKING_SOLUTION) return;
        ctrl.PlaySound(sound);
    }

    private void CheckGameEnded()
    {
        if (Config.IS_CHECKING_SOLUTION) return;

        if (Cells.Any(v => !v.AllVehicleChecked()))
        {
            return;
        }

        hintButton.interactable = false;
        resetButton.interactable = false;

        ctrl.SetCurrentLevelEnded(false);

        status = GameStatus.Ended;
        gameDef.OnGameEnded?.Invoke(this);
        StartCoroutine(ShowWinAnimation());
    }

    private IEnumerator ShowWinAnimation()
    {
        var endCells = Cells.Where(c => c.cellType == DotType.End);
        var count = endCells.Count();
        foreach (var cell in endCells)
        {
            cell.endController.ShowWin(() =>
            {
                count--;
                if (count == 0)
                {
                    foreach (var cell in Cells)
                    {
                        cell.ShowWin();
                    }
                }
            });
        }

        yield return new WaitForSeconds(0.2f);
        PlaySound(Sounds.Win);
        yield return new WaitForSeconds(0.5f);
        footer.ShowNextButton();
    }

    public void OnNextClick()
    {
        ctrl.PlaySound(Sounds.Click);
        ctrl.GoToNextLevel();
    }

    public void OnHintClick()
    {
        ctrl.PlaySound(Sounds.Button);
        var firstUnmatched = gameDef.Answer.FirstOrDefault(a => Cells.Any(u => u.cellType == DotType.Start && !u.isLocked && u.Index == a[0]));
        if (firstUnmatched != null)
        {
            // Status = GameStatus.HintChecking;
            hintButton.interactable = false;
            // hint.CheckHint((showHint, hintNumber) =>
            // {
            //     SetHintNumber(hintNumber);
            //     if (showHint)
            //     {
            selectedOrigin = Cells[firstUnmatched[0]];
            selectedOrigin.ShowAnswer(() =>
            {
                // if (Status == GameStatus.HintChecking)
                // {
                //     selectedOrigin = null;
                //     Status = GameStatus.Started;
                hintButton.interactable = true;
                // }
                CheckGameEnded();
            });
        }
    }

    public void OnResetClick()
    {
        ctrl.PlaySound(Sounds.Button);
        foreach (var dot in Cells)
        {
            dot.ResetCell();
        }
    }


    [Button]
    void CheckSolution()
    {
        var stopwatch = new Stopwatch();
        stopwatch.Start();

        Config.IS_CHECKING_SOLUTION = true;
        var visitedMaps = new HashSet<string>();
        FindPath(Cells.Where(c => c.cellType == DotType.Start).ToList(), visitedMaps);

        stopwatch.Stop();
        Debug.Log($"CheckSolution took {stopwatch.ElapsedMilliseconds}ms to execute.");
    }

    string HashMap()
    {
        return string.Join(",", Cells.Select(c => c.ConnectTo?.Index ?? -1));
    }

    void FindPath(List<DotController> occupiedCells, HashSet<string> visitedMaps)
    {
        var hash = HashMap();
        if (visitedMaps.Contains(hash))
        {
            return;
        }
        visitedMaps.Add(hash);

        if (visitedMaps.Count > 1000000)
        {
            Debug.Log($"Visited {visitedMaps.Count} maps");
            return;
        }

        if (Cells.All(v => v.AllVehicleChecked()))
        {
            Debug.Log("Found solution");
            var paths = Cells.Where(c => c.cellType == DotType.End).Select(c => c.GetPathToEnd()).ToList();

            var pathStrings = paths.Select(path => string.Join(",", path.Select(c => c.Index + 1).ToArray()));

            Debug.Log($"{string.Join("; ", pathStrings)}");
            return;
        }

        if (occupiedCells.Count == Cells.Length)
        {
            return;
        }

        foreach (var cell in occupiedCells)
        {
            if (cell.ConnectTo.AllVehicleChecked())
            {
                continue;
            }

            var adjacentCells = Cells.Where(c => !c.isBanned && c.cellType != DotType.Start && CellOperations.IsAdjacentCell(cell, c));
            foreach (var adjacentCell in adjacentCells)
            {
                if (adjacentCell.ConnectTo == null && cell.ConnectTo.Addable(adjacentCell, cell))
                {
                    cell.ConnectTo.AddCell(adjacentCell, cell);
                    var newOccupiedCells = new List<DotController>(occupiedCells) { adjacentCell };
                    FindPath(newOccupiedCells, visitedMaps);
                    cell.ConnectTo.DeleteCell(adjacentCell);
                }
            }
        }
    }
}

public enum GameStatus
{
    Ready,
    Started,
    Ended,
}