using UnityEngine;

public static class Config
{
    public const float CURSOR_Z = 0.4f;
    public const float OBJECT_Z = 1f;
    public const float ROAD_Z = 2f;

    public static Color DEFAULT_COLOR = HexToColor("#BFBFBF");

    public static Color[] COLORS = new Color[]
    {
            HexToColor("#B32424"),
            HexToColor("#B38325"),
            HexToColor("#1A8181"),
            HexToColor("#7B2ACC"),
    };

    public static Color HexToColor(string hex)
    {
        ColorUtility.TryParseHtmlString(hex, out var newCol);
        return newCol;
    }

    public static bool IS_CHECKING_SOLUTION = false;
}