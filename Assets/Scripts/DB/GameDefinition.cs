using System;
using System.Collections.Generic;
using UnityEngine;

public class GameDefinition
{
    public int[] Size { get; set; }
    public List<GroupDefinition> Groups { get; set; }
    public Action<BoardController> OnGameStarted { get; set; }
    public Action<BoardController> OnGameEnded { get; set; }
    public Action<BoardController> OnCellManuallyAdded { get; set; }
}

public class GroupDefinition
{
    public int Color { get; set; }
    public List<int> Road { get; set; }
    public List<int> Ends { get; set; }
}