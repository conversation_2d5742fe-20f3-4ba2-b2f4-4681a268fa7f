using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MusicButtonController : MonoBehaviour
{
    public AudioSource musicSource;
    public GameObject musicButton;
    public GameObject musicMuteButton;

    // Start is called before the first frame update
    void Start()
    {
        var muted = GameRecord.GetIsMusicMuted();
        musicSource.mute = muted;
        musicButton.SetActive(!muted);
        musicMuteButton.SetActive(muted);
    }

    // Update is called once per frame
    public void OnClick()
    {
        var muted = !GameRecord.GetIsMusicMuted();
        GameRecord.SetMusicMuted(muted);
        musicSource.mute = muted;
        musicButton.SetActive(!muted);
        musicMuteButton.SetActive(muted);
    }
}
