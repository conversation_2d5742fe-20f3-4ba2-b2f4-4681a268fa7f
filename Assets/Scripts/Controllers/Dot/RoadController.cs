using System.Collections;
using UnityEngine;

public class RoadController : MonoBehaviour
{
    public RoadDirection direction;
    public LineRenderer roadRenderer;
    public LineRenderer roadBackgroundRenderer;
    public float distance = 1;

    public IEnumerator Start()
    {
        yield return new WaitForSeconds(0.5f);

        switch (direction)
        {
            case RoadDirection.Up:
                roadRenderer.SetPosition(1, new Vector3(0, distance, 0));
                roadBackgroundRenderer.gameObject.SetActive(true);
                roadBackgroundRenderer.SetPosition(1, new Vector3(0, distance, 0));
                break;
            case RoadDirection.Right:
                roadRenderer.SetPosition(1, new Vector3(distance, 0, 0));
                roadBackgroundRenderer.gameObject.SetActive(true);
                roadBackgroundRenderer.SetPosition(1, new Vector3(distance, 0, 0));
                break;
            case RoadDirection.Down:
                roadRenderer.SetPosition(1, new Vector3(0, -distance, 0));
                break;
            case RoadDirection.Left:
                roadRenderer.SetPosition(1, new Vector3(-distance, 0, 0));
                break;
        }
    }

    public void SetActive(bool active, float hueColor = -1)
    {
        roadRenderer.gameObject.SetActive(active);
        if (hueColor != -1)
        {
            var color = Color.HSVToRGB(hueColor, 0.45f, 0.7f);
            roadRenderer.startColor = color;
            roadRenderer.endColor = color;
        }
    }

    public void Hide()
    {
        gameObject.SetActive(false);
    }
}



public enum RoadDirection
{
    Up = 1,
    Right = 2,
    Down = 4,
    Left = 8,
}
