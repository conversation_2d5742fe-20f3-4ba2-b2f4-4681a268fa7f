using System.Collections.Generic;
using UnityEngine;

public class SoundManagerController : MonoBehaviour
{
    public AudioSource audioData;

    private Dictionary<Sounds, string> soundMap = new Dictionary<Sounds, string>
    {
        {Sounds.Click, "Transmission"},
        {Sounds.Checked, "mouseover"},
        {Sounds.Started, "door"},
        {Sounds.Win, "trescue"},
        {Sounds.Wrong, "wrong-47985"},
        {Sounds.Button, "button"},
    };
    public void Play(Sounds sound)
    {
        var clip = Resources.Load<AudioClip>("Sounds/" + soundMap[sound]);
        audioData.PlayOneShot(clip);
    }
}

public enum Sounds
{
    Click,
    Checked,
    Started,
    Win,
    Wrong,
    Button,
}
