using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class BoardFooterController : MonoBehaviour
{
    public Button nextButton;
    public Image nextButtonImage;

    public void Init(BoardController board)
    {
        transform.localPosition = new Vector3(0, board.dotDist * (board.gameDef.Size[0] - 1) / -2 - 300, 0);
    }

    public void ShowNextButton()
    {
        nextButton.gameObject.SetActive(true);

        var startColor = new Color(1, 1, 1, 0);
        var endColor = Color.white;

        var startPosition = new Vector3(30, 100, 0);
        var targetPosition = new Vector3(380, 100, 0);

        var tween = new Tween(0.6f, (v) =>
        {
            nextButtonImage.color = Color.Lerp(startColor, endColor, v);
            nextButton.transform.localPosition = Vector3.Lerp(startPosition, targetPosition, v);
        });

        StartCoroutine(tween.Play());
    }
}
