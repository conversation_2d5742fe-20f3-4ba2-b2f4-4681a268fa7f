
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class DotStartController : MonoBehaviour
{
    public BoardController board;
    public DotController cell;
    public SpriteRenderer startRenderer;
    public GameObject symbolPrefab;
    public SpriteRenderer hintRenderer;

    public List<int> symbolIndexes;

    public List<SymbolController> symbols = new List<SymbolController>();
    public float colorHue = 0;


    public void Start()
    {
        var color = Config.GetStartBgColor(colorHue);

        startRenderer.color = color;

        var positions = new List<Vector3>();
        switch (symbolIndexes.Count)
        {
            case 1:
                positions = new List<Vector3>() { new Vector3(0, 0, Config.SYMBOL_Z) };
                break;
            case 2:
                positions = new List<Vector3>()
                {
                    new Vector3(-20, 20, Config.SYMBOL_Z),
                    new Vector3(20, -20, Config.SYMBOL_Z)
                };
                break;
            case 3:
                positions = new List<Vector3>()
                {
                    new Vector3(-22, 22, Config.SYMBOL_Z),
                    new Vector3(0, 0, Config.SYMBOL_Z),
                    new Vector3(22, -22, Config.SYMBOL_Z)
                };
                break;
            case 4:
                positions = new List<Vector3>()
                {
                     new Vector3(-22, 22, Config.SYMBOL_Z),
                     new Vector3(22, 22, Config.SYMBOL_Z),
                    new Vector3(-22, -22, Config.SYMBOL_Z),
                    new Vector3(22, -22, Config.SYMBOL_Z)
                };
                break;
        }

        symbolIndexes = symbolIndexes.OrderBy(i => i).ToList();
        for (var i = 0; i < symbolIndexes.Count; i++)
        {
            var symbolIndex = symbolIndexes[i];

            var vehicleCtrl = Instantiate(symbolPrefab, transform).GetComponent<SymbolController>();
            vehicleCtrl.transform.localPosition = positions[i];
            vehicleCtrl.symbolIndex = symbolIndex;
            vehicleCtrl.startDot = cell;
            symbols.Add(vehicleCtrl);
        }
    }

    public void Select()
    {
        if (Config.IS_CHECKING_SOLUTION)
        {
            return;
        }

        transform.localPosition = new Vector3(0, 0, Config.OBJECT_Z - 0.2f);
        transform.localScale = new Vector3(1.2f, 1.2f, 1);
    }

    public void Unselect()
    {
        if (Config.IS_CHECKING_SOLUTION)
        {
            return;
        }

        transform.localPosition = new Vector3(0, 0, Config.OBJECT_Z);
        transform.localScale = new Vector3(1, 1, 1);
    }

    public void ShowHint()
    {
        hintRenderer.gameObject.SetActive(true);
    }
}
