import { useState } from "react";
import "./App.css";

// Define 6 different colors
const COLORS = [
  "#FF4B55", // Red
  "#40AEBE", // Teal
  "#FFA726", // Orange
  "#66BB6A", // Green
  "#7E57C2", // Purple
  "#e9fa6b"  // Bright Magenta (changed from #EC407A)
];

type CellData = {
  value: number;
  color: string | null;
  isStart?: boolean;
  isHole?: boolean;
  holeColor?: number; // 0 or 1 for hole color index
  number?: number; // Add this line
};

type Fence = {
  cell1: number;
  cell2: number;
};

type MapData = {
  size: [number, number];
  ends: number[][];
  answer: number[][];
};

function App() {
  const [rows, setRows] = useState<number>(3);
  const [cols, setCols] = useState<number>(3);
  const [grid, setGrid] = useState<CellData[][]>([]);
  const [selectedColor, setSelectedColor] = useState<string | null>(null);
  const [fences, setFences] = useState<Fence[]>([]);
  const [fenceMode, setFenceMode] = useState<boolean>(false);
  const [selectedCell, setSelectedCell] = useState<number | null>(null);
  const [jsonOutput, setJsonOutput] = useState<string>("");

  // Add a new state for number mode and selected number
  const [numberMode, setNumberMode] = useState<boolean>(false);
  const [selectedNumber, setSelectedNumber] = useState<number | null>(null);

  // Add a new state for start mode
  const [startMode, setStartMode] = useState<boolean>(false);

  const generateGrid = () => {
    const newGrid = Array(rows)
      .fill(0)
      .map((_, rowIndex) =>
        Array(cols)
          .fill(0)
          .map((_, colIndex) => ({
            value: rowIndex * cols + colIndex + 1,
            color: null,
          }))
      );
    setGrid(newGrid);
    setFences([]);
    setSelectedCell(null);
    setJsonOutput("");
  };

  const handleCellClick = (rowIndex: number, colIndex: number) => {
    const cellValue = rowIndex * cols + colIndex + 1;

    if (fenceMode) {
      // Handle fence placement
      if (selectedCell === null) {
        setSelectedCell(cellValue);
      } else {
        // Check if cells are adjacent
        const selectedRow = Math.floor((selectedCell - 1) / cols);
        const selectedCol = (selectedCell - 1) % cols;

        const isAdjacent =
          (Math.abs(selectedRow - rowIndex) === 1 &&
            selectedCol === colIndex) ||
          (Math.abs(selectedCol - colIndex) === 1 && selectedRow === rowIndex);

        if (isAdjacent) {
          // Check if fence already exists
          const fenceExists = fences.some(
            (fence) =>
              (fence.cell1 === selectedCell && fence.cell2 === cellValue) ||
              (fence.cell1 === cellValue && fence.cell2 === selectedCell)
          );

          if (fenceExists) {
            // Remove fence
            setFences(
              fences.filter(
                (fence) =>
                  !(
                    (fence.cell1 === selectedCell &&
                      fence.cell2 === cellValue) ||
                    (fence.cell1 === cellValue && fence.cell2 === selectedCell)
                  )
              )
            );
          } else {
            // Add fence
            setFences([
              ...fences,
              {
                cell1: Math.min(selectedCell, cellValue),
                cell2: Math.max(selectedCell, cellValue),
              },
            ]);
          }
        }

        setSelectedCell(null);
        setJsonOutput("");
      }
    } else if (numberMode && selectedNumber !== null) {
      // Handle number placement
      const newGrid = [...grid];
      newGrid[rowIndex][colIndex].number =
        newGrid[rowIndex][colIndex].number === selectedNumber
          ? undefined
          : selectedNumber;
      setGrid(newGrid);
      setJsonOutput("");
    } else if (startMode) {
      // Handle start marking
      const newGrid = [...grid];
      newGrid[rowIndex][colIndex].isStart = !newGrid[rowIndex][colIndex].isStart;
      setGrid(newGrid);
      setJsonOutput("");
    } else if (selectedColor !== null) {
      // Handle coloring
      const newGrid = [...grid];
      newGrid[rowIndex][colIndex].color =
        newGrid[rowIndex][colIndex].color === selectedColor
          ? null
          : selectedColor;
      setGrid(newGrid);
      setJsonOutput("");
    }
  };

  const generateJson = () => {
    // First, group cells by color, start/end status, and hole color
    const colorMap: { [color: string]: CellData[] } = {};
    const startCells: CellData[] = [];
    const holeCells: { [colorIndex: number]: CellData[] } = { 0: [], 1: [] };

    // Create a map to group cells by their number
    const numberMap: { [number: number]: CellData[] } = {};

    grid.forEach((row) => {
      row.forEach((cell) => {
        // Process holes separately - a cell can be both a hole and a answer/start/end
        if (cell.isHole && cell.holeColor !== undefined) {
          holeCells[cell.holeColor].push(cell);
        }

        // Group cells by their number
        if (cell.number !== undefined) {
          if (!numberMap[cell.number]) {
            numberMap[cell.number] = [];
          }
          numberMap[cell.number].push(cell);
        }

        // Track start cells separately
        if (cell.isStart) {
          startCells.push(cell);
        }

        // Process answer
        if (cell.color) {
          if (!colorMap[cell.color]) {
            colorMap[cell.color] = [];
          }
          colorMap[cell.color].push(cell);
        }
      });
    });

    // For each color, find connected components (adjacent cells)
    const answerGroups: number[][] = [];

    // Helper function to find connected components
    const findConnectedComponents = (cells: CellData[]): number[][] => {
      const components: number[][] = [];
      const visited = new Set<number>();

      cells.forEach((cell) => {
        const cellValue = cell.value;

        if (!visited.has(cellValue)) {
          const group: number[] = [];
          const queue: CellData[] = [cell];
          visited.add(cellValue);

          while (queue.length > 0) {
            const currentCell = queue.shift()!;
            group.push(currentCell.value);

            // Find adjacent cells with the same color
            const rowIndex = Math.floor((currentCell.value - 1) / cols);
            const colIndex = (currentCell.value - 1) % cols;

            // Check all 4 directions (up, right, down, left)
            const directions = [
              [-1, 0],
              [0, 1],
              [1, 0],
              [0, -1],
            ];

            directions.forEach(([dr, dc]) => {
              const newRow = rowIndex + dr;
              const newCol = colIndex + dc;

              // Check if the new position is valid
              if (
                newRow >= 0 &&
                newRow < rows &&
                newCol >= 0 &&
                newCol < cols
              ) {
                const adjacentCell = grid[newRow][newCol];
                const adjacentValue = adjacentCell.value;

                let shouldConnect = false;

                if (
                  currentCell.color &&
                  adjacentCell.color === currentCell.color
                ) {
                  shouldConnect = true;
                }

                // Check if there's a fence between these cells
                const hasFence = fences.some(
                  (fence) =>
                    (fence.cell1 === currentCell.value &&
                      fence.cell2 === adjacentValue) ||
                    (fence.cell1 === adjacentValue &&
                      fence.cell2 === currentCell.value)
                );

                if (!hasFence && shouldConnect && !visited.has(adjacentValue)) {
                  queue.push(adjacentCell);
                  visited.add(adjacentValue);
                }
              }
            });
          }

          // Reorder the group to put start cells first
          const startIndices = group.filter(value => 
            startCells.some(startCell => startCell.value === value)
          );
          const nonStartIndices = group.filter(value => 
            !startCells.some(startCell => startCell.value === value)
          );
          
          // Combine with start cells first, then the rest
          const reorderedGroup = [...startIndices, ...nonStartIndices];
          
          components.push(reorderedGroup);
        }
      });

      return components;
    };

    // Process hole groups by color
    const holeGroups: number[][] = [];
    Object.values(holeCells).forEach((cells) => {
      if (cells.length > 0) {
        holeGroups.push(cells.map((cell) => cell.value).sort((a, b) => a - b));
      }
    });

    // Process answer groups
    Object.entries(colorMap).forEach(([, cells]) => {
      answerGroups.push(...findConnectedComponents(cells));
    });

    // Sort answer groups by their first index
    answerGroups.sort((a, b) => a[0] - b[0]);

    // Create number groups for the ends array
    const numberGroups: number[][] = [];
    Object.entries(numberMap).forEach(([, cells]) => {
      if (cells.length > 0) {
        numberGroups.push(
          cells.map((cell) => cell.value).sort((a, b) => a - b)
        );
      }
    });

    // Create the final data structure
    const mapData: MapData = {
      size: [rows, cols],
      ends: numberGroups.length > 0 ? numberGroups : [[]],
      answer: answerGroups,
    };

    // Convert to JSON string with custom formatting
    const compactJson = JSON.stringify(mapData);
    const formattedJson = compactJson
      .replace(/,"/g, ',\n"')
      .replace(/\{"/g, '{\n"')
      .replace(/\}$/, "\n}");

    setJsonOutput(formattedJson);
  };

  // Function to render fences
  const renderFences = () => {
    return fences.map((fence, index) => {
      const cell1Row = Math.floor((fence.cell1 - 1) / cols);
      const cell1Col = (fence.cell1 - 1) % cols;
      const cell2Row = Math.floor((fence.cell2 - 1) / cols);
      const cell2Col = (fence.cell2 - 1) % cols;

      const isHorizontal = cell1Row === cell2Row;

      let style;
      if (isHorizontal) {
        // Horizontal fence
        const minCol = Math.min(cell1Col, cell2Col);
        style = {
          left: `${minCol * 100 + 100}px`,
          top: `${cell1Row * 100}px`,
          width: "10px",
          height: "100px",
        };
      } else {
        // Vertical fence
        const minRow = Math.min(cell1Row, cell2Row);
        style = {
          left: `${cell1Col * 100}px`,
          top: `${minRow * 100 + 100}px`,
          width: "100px",
          height: "10px",
        };
      }

      return <div key={`fence-${index}`} className="fence" style={style} />;
    });
  };

  return (
    <div className="container">
      <h1>Grid Generator</h1>

      <div className="controls">
        <div className="input-group">
          <label htmlFor="rows">Rows:</label>
          <input
            id="rows"
            type="number"
            min="1"
            max="20"
            value={rows}
            onChange={(e) =>
              setRows(Math.max(1, parseInt(e.target.value) || 1))
            }
          />
        </div>

        <div className="input-group">
          <label htmlFor="cols">Columns:</label>
          <input
            id="cols"
            type="number"
            min="1"
            max="20"
            value={cols}
            onChange={(e) =>
              setCols(Math.max(1, parseInt(e.target.value) || 1))
            }
          />
        </div>

        <button onClick={generateGrid}>Generate Grid</button>
      </div>

      {grid.length > 0 && (
        <>
          <div className="mode-toggle">
            <button
              className={`mode-btn ${!fenceMode && !numberMode && !startMode ? 'active' : ''}`}
              onClick={() => {
                setFenceMode(false);
                setNumberMode(false);
                setStartMode(false);
              }}
            >
              Color Mode
            </button>
            <button
              className={`mode-btn ${fenceMode ? 'active' : ''}`}
              onClick={() => {
                setFenceMode(true);
                setNumberMode(false);
                setStartMode(false);
                setSelectedCell(null);
              }}
            >
              Fence Mode
            </button>
            <button
              className={`mode-btn ${numberMode ? 'active' : ''}`}
              onClick={() => {
                setFenceMode(false);
                setNumberMode(true);
                setStartMode(false);
              }}
            >
              Number Mode
            </button>
            <button
              className={`mode-btn ${startMode ? 'active' : ''}`}
              onClick={() => {
                setFenceMode(false);
                setNumberMode(false);
                setStartMode(true);
              }}
            >
              Start Mode
            </button>
          </div>

          {/* Add number buttons when in number mode */}
          {numberMode && (
            <div className="number-palette">
              {[1, 2, 3, 4, 5].map((number) => (
                <div
                  key={number}
                  className={`number-button ${
                    selectedNumber === number ? "selected" : ""
                  }`}
                  onClick={() => setSelectedNumber(number)}
                >
                  {number}
                </div>
              ))}
            </div>
          )}

          {!fenceMode && (
            <div className="color-palette">
              {COLORS.map((color, index) => (
                <div
                  key={index}
                  className={`color-swatch ${
                    selectedColor === color ? "selected" : ""
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => setSelectedColor(color)}
                />
              ))}
            </div>
          )}

          <div className="grid-container">
            <div className="grid-wrapper">
              <div
                className="grid"
                style={{
                  gridTemplateColumns: `repeat(${cols}, 1fr)`,
                  gridTemplateRows: `repeat(${rows}, 1fr)`,
                }}
              >
                {grid.map((row, rowIndex) =>
                  row.map((cell, colIndex) => (
                    <div
                      key={`${rowIndex}-${colIndex}`}
                      className={`cell ${
                        selectedCell === cell.value ? "selected-cell" : ""
                      }`}
                      style={{
                        backgroundColor: cell.color || "#f0f0f0",
                        position: "relative",
                      }}
                      onClick={() => handleCellClick(rowIndex, colIndex)}
                    >
                      {cell.value}
                      {cell.number !== undefined && (
                        <div className="cell-number">{cell.number}</div>
                      )}
                      {cell.isStart && (
                        <div className="cell-marker start-marker">S</div>
                      )}
                      {cell.isHole && (
                        <div
                          className={`cell-marker hole-marker hole-${cell.holeColor}`}
                        >
                          H{cell.holeColor! + 1}
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
              {renderFences()}
            </div>
          </div>

          <div className="json-section">
            <button onClick={generateJson} className="generate-json-btn">
              Generate JSON
            </button>

            {jsonOutput && (
              <div className="json-output">
                <h3>JSON Output:</h3>
                <textarea
                  className="json-textarea"
                  readOnly
                  value={jsonOutput}
                  onClick={(e) => {
                    const textarea = e.target as HTMLTextAreaElement;
                    textarea.select();
                    document.execCommand("copy");
                  }}
                />
                <div className="copy-hint">
                  Click in the textarea to select all and copy
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}

export default App;
