using System;
using System.Collections.Generic;
using UnityEngine;

public class GameDefinition
{
    public int[] Size { get; set; }
    public List<List<int>> Answer { get; set; }
    public List<List<int>> Ends { get; set; }
    public List<List<int>> Locked { get; set; }
    public List<int> Banned { get; set; }
    public Action<BoardController> OnGameStarted { get; set; }
    public Action<BoardController> OnGameEnded { get; set; }
    public Action<BoardController> OnDotManuallyAdded { get; set; }
    public Action<BoardController, DotController> OnDotConnected { get; set; }
}