using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using Random = UnityEngine.Random;

public class DotController : MonoBehaviour
{
    public SpriteRenderer dotCenterRenderer;
    public SpriteRenderer visitedCircleRenderer;
    public SpriteRenderer endingCircleRenderer;
    public TMP_Text wrongText;
    public DotType cellType = DotType.Road;
    public DotRoadController roadController;
    public DotStartController startController;
    public DotEndController endController;
    public List<int> symbols;

    public BoardController board;

    public int Index;
    public int I;
    public int J;

    public List<int> answer = new List<int>();
    public bool isLocked = false;
    public bool isChecked = false;
    public bool isBanned = false;
    private DotController _connectTo = null;
    public DotController previousCell = null;
    public DotController ConnectTo
    {
        get
        {
            return _connectTo;
        }
    }

    private bool showingWrong = false;
    private List<DotController> _occupiedCells = new List<DotController>();
    public List<DotController> OccupiedCells
    {
        get
        {
            return _occupiedCells;
        }
        set
        {
            // foreach (var cell in value)
            // {
            //     cell.RefreshConnectedCells(value);
            // }

            _occupiedCells = value;
        }
    }

    private Tween showVisited;


    void Awake()
    {
        var startScale = 50f;
        var targetScale = 100f;

        showVisited = new Tween(0.5f, (v) =>
        {
            var scale = Mathf.Lerp(startScale, targetScale, v);
            var color = visitedCircleRenderer.color;
            color.a = 1 - v;

            visitedCircleRenderer.gameObject.transform.localScale = new Vector3(scale, scale, 1);
            visitedCircleRenderer.color = color;
        });

        showVisited.OnStarted += (tween) =>
        {
            visitedCircleRenderer.gameObject.SetActive(true);
        };

        showVisited.OnCompleted += (tween) =>
        {
            visitedCircleRenderer.gameObject.SetActive(false);
        };

        showVisited.OnPaused += (tween) =>
        {
            visitedCircleRenderer.gameObject.SetActive(false);
        };
    }

    // Start is called before the first frame update
    void Start()
    {
        roadController.cell = this;
        roadController.board = board;

        var x = transform.localPosition.x;
        var y = transform.localPosition.y;

        var targetPosition = new Vector3(x, y, Config.OBJECT_Z);
        var startPosition = new Vector3(x * 5, y * 5, Config.OBJECT_Z);

        transform.localPosition = startPosition;

        // yield return null;

        var showup = new Tween(0.5f, (v) =>
        {
            transform.localPosition = Vector3.Lerp(startPosition, targetPosition, v);
        });
        StartCoroutine(showup.Play());
    }

    public void Select()
    {
        startController?.Select();
    }
    public void Unselect()
    {
        startController?.Unselect();
    }

    public bool Addable(DotController cell, DotController previousCell)
    {
        if (previousCell?.cellType == DotType.End)
        {
            return false;
        }
        if (!CellOperations.IsAdjacentCell(cell, previousCell))
        {
            return false;
        }

        if (cell.cellType == DotType.End)
        {
            var vehicle = startController.symbols.Any(v => v.IsChecked == false && v.symbolIndex == cell.endController.symbolIndex);
            if (!vehicle)
            {
                return false;
            }
        }

        return true;
    }

    public void AddCell(DotController cell, DotController previousCell)
    {
        cell.SetConnectTo(this, previousCell);
        OccupiedCells = new List<DotController>(OccupiedCells)
        {
            cell
        };
        if (previousCell != null)
        {
            cell.roadController.SetPreviousCell(previousCell);
        }

        if (cell.cellType == DotType.End)
        {
            var vehicle = startController.symbols.FirstOrDefault(v => v.IsChecked == false && v.symbolIndex == cell.endController.symbolIndex);
            if (vehicle != null)
            {
                board.PlaySound(Sounds.Checked);
                vehicle.targetDot = cell;
                vehicle.IsChecked = true;
                cell.endController.SetChecked(true);
            }
        }
    }

    private List<DotController> GetAllFollowingCells(DotController deletingCell)
    {
        var cells = new List<DotController> { deletingCell };
        var moreDeletingCells = OccupiedCells.Where(c => deletingCell.Index == c.previousCell?.Index).ToList();

        while (moreDeletingCells.Any())
        {
            cells.AddRange(moreDeletingCells);
            moreDeletingCells = OccupiedCells.Where(c => moreDeletingCells.Any(more => more.Index == c.previousCell?.Index)).ToList();
        }

        return cells;
    }

    public bool DeleteCell(DotController deletingCell)
    {
        if (deletingCell.cellType == DotType.Start || deletingCell.ConnectTo?.isLocked == true)
        {
            return false;
        }

        // if (OccupiedCells.Any(c => c.previousCell?.Index == deletingCell.Index))
        // {
        //     return false;
        // }

        var deletingCells = GetAllFollowingCells(deletingCell);
        deletingCells.Reverse();

        var temp = new List<DotController>(OccupiedCells.Where(c => !deletingCells.Any(d => d.Index == c.Index)));
        OccupiedCells = temp;

        foreach (var d in deletingCells)
        {
            d.roadController.RemovePreviousCell();
            d.SetConnectTo(null, null);

            if (d.cellType == DotType.End)
            {
                var vehicle = startController.symbols.FirstOrDefault(v => v.IsChecked && v.targetDot == d);
                if (vehicle != null)
                {
                    vehicle.IsChecked = false;
                    d.endController.SetChecked(false);
                }
            }
        }

        return true;
    }

    void SetConnectTo(DotController startCell, DotController previousCell)
    {
        // occupiedObject.SetActive(occupiedBy != null);
        // centerObject.transform.localScale = occupiedBy != null
        //     ? new Vector3(0.3f, 0.3f, 1)
        //     : new Vector3(1, 1, 1);

        _connectTo = startCell;
        this.previousCell = previousCell;
        if (previousCell != null)
        {
            showVisited.Position = 0;
            visitedCircleRenderer.color = Config.GetStartBgColor(previousCell.ConnectTo.startController.colorHue);
            StartCoroutine(showVisited.Play());
        }
        else
        {
            showVisited.Pause();
        }
        // if (occupiedBy != null)
        // {
        //     if (occupiedBy.isLocked)
        //     {
        //         centerObject.SetActive(false);
        //         lockedObject.SetActive(true);
        //         var lineColor = Color.HSVToRGB(occupiedBy.ColorHue, 0.2f, 0.8f);
        //         foreach (var line in lockedLineRenders)
        //         {
        //             line.startColor = lineColor;
        //             line.endColor = lineColor;
        //         }
        //     }
        //     centerFlashAnimation.Pause();
        //     centerRenderer.color = Color.HSVToRGB(occupiedBy.ColorHue, 0.2f, 0.9f);
        //     ColorHue = occupiedBy.ColorHue;
        // }
        // else
        // {
        //     ColorValue = COLOR_VALUE_NORMAL;
        //     StartCoroutine(centerFlashAnimation.Loop());
        // }
    }

    public void SetStart(List<int> answer, List<List<int>> ends, float colorHue, bool isLocked)
    {
        cellType = DotType.Start;

        this.answer = answer;
        this.isLocked = isLocked;
        AddCell(this, null);

        var vehicleColors = new List<int>();
        foreach (var a in answer)
        {
            var end = ends.FindIndex(e => e.Any(i => i == a));
            if (end >= 0)
            {
                vehicleColors.Add(end);
            }
        }

        startController.gameObject.SetActive(true);
        startController.cell = this;
        startController.board = board;
        startController.symbolIndexes = vehicleColors;
        startController.colorHue = colorHue;
        dotCenterRenderer.gameObject.SetActive(false);

        // numberText.gameObject.SetActive(true);
        // centerObject.gameObject.SetActive(false);

        // StartCoroutine(SetOriginAddCell());
        // InitWinAnimation();
    }

    public void SetEnd(int symbolIndex)
    {
        cellType = DotType.End;
        endController.gameObject.SetActive(true);
        endController.symbolIndex = symbolIndex;
        endController.cell = this;
        dotCenterRenderer.gameObject.SetActive(false);
    }

    public void ShowWrong()
    {
        if (showingWrong)
        {
            return;
        }

        var step = 0.4f;
        var startColor = new Color(0.75f, 0.15f, 0.15f, 0);
        var endColor = new Color(0.75f, 0.15f, 0.15f, 1);

        var tween = new Tween(2f, (v) =>
        {
            if (v < step)
            {
                wrongText.color = Color.Lerp(startColor, endColor, v / step);
            }
            else
            {
                wrongText.color = Color.Lerp(endColor, startColor, (v - step) / (1 - step));
            }
        });
        tween.OnStarted += ((_) =>
        {
            showingWrong = true;
            board.PlaySound(Sounds.Wrong);
        });
        tween.OnCompleted += ((_) =>
        {
            showingWrong = false;
        });
        StartCoroutine(tween.Play());
    }

    public List<DotController> GetPathToEnd()
    {
        if (cellType != DotType.End)
        {
            return null;
        }

        var path = new List<DotController>();
        var cell = this;
        while (cell != null)
        {
            path.Add(cell);
            cell = cell.previousCell;
        }

        path.Reverse();
        return path;
    }

    public bool AllVehicleChecked()
    {
        if (cellType == DotType.Start)
        {
            return startController.symbols.All(v => v.IsChecked);
        }

        return true;
    }

    public void ShowWinCircle(float duration)
    {
        var color = Config.GetStartBgColor(ConnectTo.startController.colorHue);
        endingCircleRenderer.color = color;
        endingCircleRenderer.transform.localScale = new Vector3(0f, 0f, 1);
        endingCircleRenderer.gameObject.SetActive(true);

        var showGameEndAnimation = new Tween(duration, (v) =>
        {
            var scale = Mathf.Lerp(0f, 100f, v);
            if (v > 0.5f)
            {
                color.a = 1 - (v - 0.5f) * 2f;
            }
            else
            {
                color.a = 1f;
            }

            endingCircleRenderer.gameObject.transform.localScale = new Vector3(scale, scale, 1);
            endingCircleRenderer.color = color;
        });


        StartCoroutine(showGameEndAnimation.Play());
    }

    public void ShowWin()
    {
        if (ConnectTo == null)
        {
            return;
        }

        showVisited.Pause();
        showVisited.Position = 0;

        var color = Config.GetStartBgColor(ConnectTo.startController.colorHue);
        endingCircleRenderer.color = color;
        endingCircleRenderer.transform.localScale = new Vector3(0f, 0f, 1);
        endingCircleRenderer.gameObject.SetActive(true);

        var showGameEndAnimation = new Tween(Random.Range(1f, 2f), (v) =>
        {
            var scale = Mathf.Lerp(0f, 100f, v);
            color.a = 1 - v;

            endingCircleRenderer.gameObject.transform.localScale = new Vector3(scale, scale, 1);
            endingCircleRenderer.color = color;
        });

        showGameEndAnimation.PauseOnStarted = Random.Range(1f, 4f);

        showGameEndAnimation.OnCompleted += (tween) =>
        {
            tween.Duration = Random.Range(1f, 2f);
            tween.PauseOnStarted = Random.Range(3f, 6f);
            tween.Position = 0;
            StartCoroutine(tween.Play());
        };

        StartCoroutine(showGameEndAnimation.Play());
    }

    public void ResetCell()
    {
        if (isBanned)
        {
            return;
        }

        if (cellType == DotType.Start)
        {
            if (isLocked)
            {
                return;
            }

            foreach (var symbolCtrl in startController.symbols)
            {
                symbolCtrl.IsChecked = false;
                symbolCtrl.targetDot = null;
            }
            OccupiedCells = new List<DotController> { this };
            return;
        }


        if (ConnectTo?.isLocked == true)
        {
            return;
        }

        roadController.RemovePreviousCell();
        SetConnectTo(null, null);

        if (cellType == DotType.End)
        {
            endController.SetChecked(false);
        }
    }


    public void ShowAnswer(Action done)
    {
        foreach (var cell in OccupiedCells)
        {
            cell.ResetCell();
        }

        isLocked = true;
        isChecked = true;

        startController.ShowHint();

        StartCoroutine(ShowAnswerAnimation(done));
    }

    private IEnumerator ShowAnswerAnimation(Action done)
    {
        var waitingCells = new Queue<int>(answer.Skip(1));
        while (waitingCells.Any())
        {
            var cellIndex = waitingCells.Dequeue();
            var cell = board.Cells[cellIndex];
            var previous = OccupiedCells.FirstOrDefault(p => Addable(cell, p));
            if (previous != null)
            {
                if (cell.ConnectTo != null)
                {
                    cell.ConnectTo.DeleteCell(cell);
                }
                AddCell(cell, previous);
                yield return new WaitForSeconds(0.1f);
            }
            else
            {
                waitingCells.Enqueue(cellIndex);
            }
        }

        done();
    }

}

public enum DotType
{
    Start,
    End,
    Road,

}
