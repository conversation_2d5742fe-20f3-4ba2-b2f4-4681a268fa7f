using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;

public class DotController : MonoBehaviour
{
    public TMP_Text wrongText;
    public DotType cellType = DotType.Road;
    public DotRoadController roadController;
    public DotStartController startController;
    public DotEndController endController;

    public BoardController board;

    public int Index;
    public int I;
    public int J;

    public float X
    {
        get
        {
            return transform.localPosition.x / 100f;
        }
    }
    public float Y
    {
        get
        {
            return -transform.localPosition.y / 100f;
        }
    }


    public List<int> answer = new List<int>();
    public bool isLocked = false;
    public bool isChecked = false;
    public bool isBanned = false;
    private DotController _connectTo = null;
    public DotController previousCell = null;
    public DotController ConnectTo
    {
        get
        {
            return _connectTo;
        }
    }

    private bool showingWrong = false;
    private List<DotController> _occupiedCells = new List<DotController>();
    public List<DotController> OccupiedCells
    {
        get
        {
            return _occupiedCells;
        }
        set
        {
            // foreach (var cell in value)
            // {
            //     cell.RefreshConnectedCells(value);
            // }

            _occupiedCells = value;
        }
    }

    // Start is called before the first frame update
    void Start()
    {
        roadController.cell = this;
        roadController.board = board;
        switch (cellType)
        {
            case DotType.Road:
                // roadController.cell = this;
                break;
            case DotType.End:
                endController.gameObject.SetActive(true);
                // endController.cell = this;
                break;
            case DotType.Start:
                startController.gameObject.SetActive(true);
                startController.cell = this;
                startController.board = board;
                break;
        }
    }

    public void Select()
    {
        startController?.Select();
    }
    public void Unselect()
    {
        startController?.Unselect();
    }

    public bool Addable(DotController cell, DotController previousCell)
    {
        if (previousCell?.cellType == DotType.End)
        {
            return false;
        }
        if (!CellOperations.IsAdjacentCell(cell, previousCell))
        {
            return false;
        }

        if (cell.cellType == DotType.End)
        {
            return cell.endController.colorIndex == startController.colorIndex;
        }

        return true;
    }

    public void AddCell(DotController cell, DotController previousCell)
    {
        cell.SetConnectTo(this, previousCell);
        OccupiedCells = new List<DotController>(OccupiedCells)
        {
            cell
        };
        if (previousCell != null)
        {
            cell.roadController.SetPreviousCell(previousCell);
        }

        if (cell.cellType == DotType.End)
        {
            board.PlaySound(Sounds.Checked);
            startController.SetProgress(true);
        }
    }

    private List<DotController> GetAllFollowingCells(DotController deletingCell)
    {
        var cells = new List<DotController> { deletingCell };
        var moreDeletingCells = OccupiedCells.Where(c => deletingCell.Index == c.previousCell?.Index).ToList();

        while (moreDeletingCells.Any())
        {
            cells.AddRange(moreDeletingCells);
            moreDeletingCells = OccupiedCells.Where(c => moreDeletingCells.Any(more => more.Index == c.previousCell?.Index)).ToList();
        }

        return cells;
    }

    public bool DeleteCell(DotController deletingCell)
    {
        if (deletingCell.cellType == DotType.Start || deletingCell.ConnectTo?.isLocked == true)
        {
            return false;
        }

        // if (OccupiedCells.Any(c => c.previousCell?.Index == deletingCell.Index))
        // {
        //     return false;
        // }

        var deletingCells = GetAllFollowingCells(deletingCell);
        deletingCells.Reverse();

        var temp = new List<DotController>(OccupiedCells.Where(c => !deletingCells.Any(d => d.Index == c.Index)));
        OccupiedCells = temp;


        foreach (var d in deletingCells)
        {
            d.roadController.RemovePreviousCell();
            d.SetConnectTo(null, null);

            if (d.cellType == DotType.End)
            {
                startController.SetProgress(false);
            }
        }

        return true;
    }

    void SetConnectTo(DotController startCell, DotController previousCell)
    {
        // occupiedObject.SetActive(occupiedBy != null);
        // centerObject.transform.localScale = occupiedBy != null
        //     ? new Vector3(0.3f, 0.3f, 1)
        //     : new Vector3(1, 1, 1);

        _connectTo = startCell;
        this.previousCell = previousCell;
        // if (occupiedBy != null)
        // {
        //     if (occupiedBy.isLocked)
        //     {
        //         centerObject.SetActive(false);
        //         lockedObject.SetActive(true);
        //         var lineColor = Color.HSVToRGB(occupiedBy.ColorHue, 0.2f, 0.8f);
        //         foreach (var line in lockedLineRenders)
        //         {
        //             line.startColor = lineColor;
        //             line.endColor = lineColor;
        //         }
        //     }
        //     centerFlashAnimation.Pause();
        //     centerRenderer.color = Color.HSVToRGB(occupiedBy.ColorHue, 0.2f, 0.9f);
        //     ColorHue = occupiedBy.ColorHue;
        // }
        // else
        // {
        //     ColorValue = COLOR_VALUE_NORMAL;
        //     StartCoroutine(centerFlashAnimation.Loop());
        // }
    }

    public void SetStart(GroupDefinition group)
    {
        cellType = DotType.Start;

        this.answer = group.Road;
        // this.isLocked = isLocked;
        AddCell(this, null);

        startController.colorIndex = group.Color;
        startController.count = group.Ends.Count;

        // numberText.gameObject.SetActive(true);
        // centerObject.gameObject.SetActive(false);

        // StartCoroutine(SetOriginAddCell());
        // InitWinAnimation();
    }

    public void SetEnd(int colorIndex)
    {
        cellType = DotType.End;
        endController.colorIndex = colorIndex;
    }

    public void ShowWrong()
    {
        if (showingWrong)
        {
            return;
        }

        var step = 0.4f;
        var startColor = new Color(0.75f, 0.15f, 0.15f, 0);
        var endColor = new Color(0.75f, 0.15f, 0.15f, 1);

        var tween = new Tween(2f, (v) =>
        {
            if (v < step)
            {
                wrongText.color = Color.Lerp(startColor, endColor, v / step);
            }
            else
            {
                wrongText.color = Color.Lerp(endColor, startColor, (v - step) / (1 - step));
            }
        });
        tween.OnStarted += ((_) =>
        {
            showingWrong = true;
            board.PlaySound(Sounds.Wrong);
        });
        tween.OnCompleted += ((_) =>
        {
            showingWrong = false;
        });
        StartCoroutine(tween.Play());
    }

    public List<DotController> GetPathToEnd()
    {
        if (cellType != DotType.End)
        {
            return null;
        }

        var path = new List<DotController>();
        var cell = this;
        while (cell != null)
        {
            path.Add(cell);
            cell = cell.previousCell;
        }

        path.Reverse();
        return path;
    }

    public bool AllVehicleChecked()
    {
        if (cellType == DotType.Start)
        {
            // return startController.vehicles.All(v => v.isChecked);
            return false;
        }

        return true;
    }
}

public enum DotType
{
    Start,
    End,
    Road,

}
