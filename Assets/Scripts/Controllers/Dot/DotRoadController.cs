using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class DotRoadController : MonoBehaviour
{
    public DotController cell;
    public BoardController board;
    public SpriteRenderer dotCenterRenderer;
    public RoadController roadUp;
    public RoadController roadRight;
    public RoadController roadDown;
    public RoadController roadLeft;

    void Start()
    {
        if (cell.I <= 0)
        {
            roadUp.Hide();
        }
        if (cell.I >= board.gameDef.Size[0] - 1)
        {
            roadDown.Hide();
        }
        if (cell.J <= 0)
        {
            roadLeft.Hide();
        }
        if (cell.J >= board.gameDef.Size[1] - 1)
        {
            roadRight.Hide();
        }

        roadUp.distance = board.dotDist / 100f;
        roadRight.distance = board.dotDist / 100f;
        roadDown.distance = board.dotDist / 100f;
        roadLeft.distance = board.dotDist / 100f;
    }


    public void SetPreviousCell(DotController previousCell)
    {
        if (Config.IS_CHECKING_SOLUTION)
        {
            return;
        }

        var colorIndex = previousCell.ConnectTo.startController.colorIndex;
        HideAllRoads();
        if (previousCell.I < cell.I)
        {
            roadUp.SetActive(true, colorIndex);
        }
        else if (previousCell.I > cell.I)
        {
            roadDown.SetActive(true, colorIndex);
        }
        else if (previousCell.J < cell.J)
        {
            roadLeft.SetActive(true, colorIndex);
        }
        else if (previousCell.J > cell.J)
        {
            roadRight.SetActive(true, colorIndex);
        }
        dotCenterRenderer.color = Config.COLORS[colorIndex];
    }

    public void RemovePreviousCell()
    {
        if (Config.IS_CHECKING_SOLUTION)
        {
            return;
        }

        HideAllRoads();
        dotCenterRenderer.color = Config.DEFAULT_COLOR;
    }

    private void HideAllRoads()
    {
        roadUp.SetActive(false);
        roadRight.SetActive(false);
        roadDown.SetActive(false);
        roadLeft.SetActive(false);
    }
}
