using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public static class Database
{
    private static string[] gameListStart = new string[]
    {
        "0-1",          "3-1"/*14*/,        "43-1"/*28*/,       "34-1"/*89*/,       "4-4"/*185*/,
        "4-1"/*224*/,   "4-2"/*264*/,       "4-5"/*809*/,       "4-6"/*1010*/,      "45-2"/*1028*/,
        "4-3"/*1079*/,  "54-2"/*18060*/,    "54-3"/*40153*/,    "54-1"/*51026*/,    "45-1"/*80575*/,
       "64-2"/*37772*/, "64-1"/*161661*/, "46-1",           "5-2",        "5-8",         "5-6",              "5-1",
        "5-3",       "5-5",              "5-4",              "5-7",              "56-1",
        "65-1",    "65-2",     "6-1",              "6-2",              "6-3",              
        "6-5",  "6-7",  "6-8","6-4",  "76-1","76-2",    "7-1",              "7-2",              "8-1",              "8-2",
    };

    private static string[] gameListHard = new string[]
    {
    };

    public static string[] GetList(LevelGroup group)
    {
        switch (group)
        {
            case LevelGroup.Hard:
                return gameListHard;
            case LevelGroup.Start:
            default:
                return gameListStart;
        }
    }

    public static (int, string) GetLevel(LevelGroup group, string level)
    {
        var gameList = GetList(group);
        if (string.IsNullOrEmpty(level))
        {
            var resolved = GameRecord.GetAllResolved(group).Select(r => r.Game).ToArray();
            return FindFirstUnresolved(gameList, resolved);
        }

        var index = Array.IndexOf(gameList.ToArray(), level);
        if (index < 0)
        {
            var resolved = GameRecord.GetAllResolved(group).Select(r => r.Game).ToArray();
            return FindFirstUnresolved(gameList, resolved);
        }
        return (index + 1, level);
    }

    public static (int, string) LoadNext(LevelGroup group, string currentLevel)
    {
        var gameList = GetList(group);
        var index = Array.IndexOf(gameList.ToArray(), currentLevel);
        if (index < gameList.Length - 1)
        {
            return (index + 2, gameList[index + 1]);
        }

        return (0, string.Empty);
    }

    private static (int, string) FindFirstUnresolved(string[] list, string[] resolved)
    {
        for (var i = 0; i < list.Length; i++)
        {
            var l = list[i];
            if (!resolved.Any(r => r == l))
            {
                return (i + 1, l);
            }
        }

        return (0, string.Empty);
    }

    public static GameDefinition LoadMap(string mapName)
    {
        var json = Resources.Load<TextAsset>("Maps/" + mapName)?.text;

        var gameDef = Newtonsoft.Json.JsonConvert.DeserializeObject<GameDefinition>(json);
        gameDef.Banned = gameDef.Banned?.Select(t => t - 1).ToList() ?? new List<int>();
        gameDef.Locked = gameDef.Locked?.Select(l => l.Select(t => t - 1).ToList()).ToList() ?? new List<List<int>>();
        gameDef.Answer = gameDef.Answer.OrderByDescending(a => a.Count).Select(l => l.Select(t => t - 1).ToList()).ToList();
        gameDef.Ends = gameDef.Ends.Select(l => l.Select(t => t - 1).ToList()).ToList();

        switch (mapName)
        {
            case "0-1":
                gameDef = HelpMaps.GetHelp1(gameDef);
                break;
        }

        return gameDef;

    }
}

public enum LevelGroup
{
    Start,
    Hard
}
