using UnityEngine;

public static class Config
{
    public const float CURSOR_Z = 0.4f;
    public const float OBJECT_Z = 1f;
    public const float ROAD_Z = 2f;
    public const float SYMBOL_Z = -0.1f;


    public static Color DEFAULT_COLOR = HexToColor("#BFBFBF");

    public static Color GetStartBgColor(float colorHue)
    {
        return Color.HSVToRGB(colorHue, 0.4f, 0.7f);
    }

    public static Color HexToColor(string hex)
    {
        ColorUtility.TryParseHtmlString(hex, out var newCol);
        return newCol;
    }

    public static bool IS_CHECKING_SOLUTION = false;
}