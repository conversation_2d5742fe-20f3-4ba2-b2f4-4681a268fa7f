using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GameRunnerController : MonoBehaviour
{
    public RectTransform canvasRectTransform;
    public BoardController currentBoard;
    public SoundManagerController soundManager;
    public GameObject boardPrefab;
    public GameObject canvasContainer;
    public bool autoStart = true;


    // Start is called before the first frame update
    void Start()
    {
        if (autoStart)
        {
            GotoPlay(LevelGroup.Start);
        }
    }

    public Vector2 GetMousePosition()
    {
        Vector2 vector;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(canvasRectTransform, Input.mousePosition, Camera.main, out vector);
        return vector;
    }

    public void PlaySound(Sounds sound)
    {
        soundManager.Play(sound);
    }

    public void ShowBoard(LevelGroup group, int level, string mapName)
    {
        var nextBoard = Instantiate(boardPrefab, canvasContainer.transform).GetComponent<BoardController>();
        nextBoard.transform.localPosition = new Vector3(0, 0, 0);
        nextBoard.transform.localScale = new Vector3(1, 1, 1);
        nextBoard.levelGroup = group;
        nextBoard.mapName = mapName;
        nextBoard.level = level;
        nextBoard.ctrl = this;

        currentBoard = nextBoard;
        GameRecord.SetCurrentLevel(group, mapName);

        var groupString = group == LevelGroup.Start ? "start" : "hard";
        // Debug.Log($"OnGameStarted {groupString}-{index + 1}");
        // TinySauce.OnGameStarted($"{groupString}-{index + 1}");
    }

    private void GotoPlay(LevelGroup group)
    {
        var currentLevel = GameRecord.GetCurrentLevel(group);
        var (level, name) = Database.GetLevel(group, currentLevel);
        if (level <= 0)
        {
            level = 1;
            name = "0-1";
        }
        ShowBoard(group, level, name);
    }

    public void GoToNextLevel()
    {
        var (nextLevel, nextName) = Database.LoadNext(currentBoard.levelGroup, currentBoard.mapName);
        if (string.IsNullOrEmpty(nextName))
        {
            nextLevel = 1;
            nextName = "0-1";
        }

        var targetPosition = 3000f;
        var moveCurrentBoardTween = new Tween(0.5f, (value) =>
        {
            currentBoard.transform.localPosition = new Vector3(targetPosition * -value, 0, 0);
        });

        moveCurrentBoardTween.OnCompleted += (_) =>
        {
            Destroy(currentBoard.gameObject);
            ShowBoard(currentBoard.levelGroup, nextLevel, nextName);
        };

        StartCoroutine(moveCurrentBoardTween.Play());
    }


    public void SetCurrentLevelEnded(bool isPerfect)
    {
        var resolvedCount = GameRecord.SetGameEnded(currentBoard.levelGroup, currentBoard.mapName, isPerfect);
        var groupString = currentBoard.levelGroup == LevelGroup.Start ? "start" : "challenge";
        // Debug.Log($"OnGameFinished {groupString}-{currentBoard.level}");
        // TinySauce.OnGameFinished(true, isPerfect ? 1 : 0, $"{groupString}-{currentBoard.level}");

        var (_, nextName) = Database.LoadNext(currentBoard.levelGroup, currentBoard.mapName);
        GameRecord.SetCurrentLevel(currentBoard.levelGroup, nextName);
    }
}
