#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.color-palette {
  display: flex;
  gap: 10px;
  margin: 20px 0;
  justify-content: center;
}

.color-swatch {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: transform 0.2s, border-color 0.2s;
}

.color-swatch:hover {
  transform: scale(1.1);
}

.color-swatch.selected {
  border-color: #333;
  transform: scale(1.1);
}

.mode-toggle {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  gap: 10px;
}

.mode-btn {
  padding: 8px 16px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.mode-btn.active {
  background-color: #4a90e2;
  color: white;
  border-color: #4a90e2;
}

.grid-wrapper {
  position: relative;
  display: inline-block;
}

.fence {
  position: absolute;
  background-color: #333;
  z-index: 10;
}

.selected-cell {
  box-shadow: inset 0 0 0 4px yellow;
}

.cell {
  position: relative;
  z-index: 1;
  background-color: #f0f0f0;
  border: 2px solid #333;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cell:hover {
  opacity: 0.9;
}

.cell-marker {
  position: absolute;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
}

.start-marker {
  background-color: #4CAF50;
  color: white;
  top: 5px;
  left: 5px;
}

.end-marker {
  background-color: #F44336;
  color: white;
  top: 5px;
  right: 5px;
}

.hole-marker {
  bottom: 5px;
  left: 5px;
  color: white;
}

.hole-0 {
  background-color: #FF4B55;
}

.hole-1 {
  background-color: #40AEBE;
}

.grid {
  display: grid;
  gap: 0;
  width: fit-content;
}

.grid-container {
  margin-top: 30px;
  overflow: auto;
  display: flex;
  justify-content: center;
}

.json-section {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.generate-json-btn {
  margin-bottom: 20px;
  padding: 10px 20px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.generate-json-btn:hover {
  background-color: #45a049;
}

.json-output {
  width: 100%;
  max-width: 800px;
  text-align: left;
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 15px;
  border: 1px solid #ddd;
}

.json-textarea {
  width: 100%;
  min-height: 300px;
  font-family: monospace;
  font-size: 14px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  resize: vertical;
  white-space: pre;
  overflow-x: auto;
}

.copy-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  text-align: center;
}

.controls {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.input-group label {
  font-weight: bold;
}

.input-group input {
  width: 60px;
  height: 36px;
  padding: 5px 10px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  text-align: center;
}

.input-group input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* Add styles for number mode */
.number-palette {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  gap: 10px;
}

.number-button {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background-color: #f0f0f0;
  border: 2px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.number-button:hover {
  background-color: #e0e0e0;
}

.number-button.selected {
  border-color: #333;
  background-color: #4a90e2;
  color: white;
}

.cell-number {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #333;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
}

/* Add styles for start marker */
.start-marker {
  background-color: #4CAF50;
  color: white;
  top: 5px;
  left: 5px;
}
