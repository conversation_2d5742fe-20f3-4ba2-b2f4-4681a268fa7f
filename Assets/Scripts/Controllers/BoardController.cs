using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.U2D;

public class BoardController : MonoBehaviour
{
    public string mapName;
    public GameObject dotPrefab;
    public GameRunnerController ctrl;
    public DotController[] Cells;

    public const float BOARD_WIDTH = 1000f;
    public float dotDist = 150f;

    private DotController selectedOrigin = null;
    private DotController currentCell = null;
    private DotController previousCell = null;

    public GameDefinition gameDef;
    public GameObject cursorRenderer;


    // Start is called before the first frame update
    void Start()
    {
        gameDef = Database.LoadMap(mapName);

        var rowNumber = gameDef.Size[0];
        var colNumber = gameDef.Size[1];

        var max = Mathf.Max(rowNumber, colNumber);

        switch (max)
        {
            case 8:
                dotDist = 130f;
                break;
            default:
                dotDist = 150f;
                break;
        }

        var actualWidth = (colNumber - 1) * dotDist;
        var actualHeight = (rowNumber - 1) * dotDist;
        var startX = actualWidth / -2f;
        var startY = actualHeight / 2f;

        Cells = new DotController[colNumber * rowNumber];
        for (var i = 0; i < rowNumber; i++)
        {
            for (var j = 0; j < colNumber; j++)
            {
                var ctrl = Instantiate(dotPrefab, transform).GetComponent<DotController>();

                ctrl.transform.localPosition = new Vector3(j * dotDist + startX, -i * dotDist + startY, 0);
                ctrl.I = i;
                ctrl.J = j;

                var index = i * colNumber + j;
                ctrl.Index = index;

                Cells[index] = ctrl;
                ctrl.board = this;

                // if (gameDef.Banned?.Any(b => b == index) == true)
                // {
                //     ctrl.isBanned = true;
                //     ctrl.gameObject.SetActive(false);
                //     continue;
                // }

                var groupStart = gameDef.Groups.FirstOrDefault(g => g.Road.First() == index);
                if (groupStart != null)
                {
                    ctrl.SetStart(groupStart);
                    continue;
                }

                var groupEnd = gameDef.Groups.FirstOrDefault(g => g.Ends.Any(e => e == index));
                // var colorIndex = gameDef.Ends.FindIndex(e => e.Any(i => i == index));
                if (groupEnd != null)
                {
                    ctrl.SetEnd(groupEnd.Color);
                    continue;
                }

                ctrl.cellType = DotType.Road;
            }
        }

        PlaySound(Sounds.Started);
        gameDef.OnGameStarted?.Invoke(this);
    }

    void Update()
    {
        HandleInput();
    }

    private void Unselect()
    {
        selectedOrigin?.Unselect();
        selectedOrigin = null;
        previousCell = null;
        currentCell = null;
        cursorRenderer.gameObject.SetActive(false);
    }

    private void UpdateCursor()
    {
        var pos = ctrl.GetMousePosition();
        cursorRenderer.transform.localPosition = new Vector3(pos.x, pos.y, Config.CURSOR_Z);
    }

    private void HandleInput()
    {
        // if (Status != GameStatus.Started)
        // {
        //     return;
        // }

        if (selectedOrigin == null)
        {
            if (Input.GetMouseButtonDown(0))
            {
                var selectedCell = GetCell();
                if (selectedCell != null)
                {
                    if (selectedCell.isBanned)
                    {
                        return;
                    }

                    if (selectedCell.ConnectTo != null && !selectedCell.ConnectTo.isLocked)
                    {
                        selectedOrigin = selectedCell.ConnectTo;
                        selectedOrigin.Select();
                        // previousMouseOverCell = selectedCell;
                        previousCell = selectedCell;

                        cursorRenderer.gameObject.SetActive(true);
                        UpdateCursor();
                    }
                }
            }

            return;
        }

        if (Input.GetMouseButtonUp(0))
        {
            Unselect();
            return;
        }

        UpdateCursor();
        var cell = GetCell();
        if (cell == null || cell == previousCell || cell == currentCell || cell.isBanned)
        {
            return;
        }
        currentCell = cell;
        // UpdateCursorDirection();

        if (cell.ConnectTo == null)
        {
            if (selectedOrigin.Addable(cell, previousCell))
            {
                selectedOrigin.AddCell(cell, previousCell);
                previousCell = cell;
                gameDef.OnCellManuallyAdded?.Invoke(this);
                CheckGameEnded();
            }
            else
            {
                Unselect();
                cell.ShowWrong();
            }
        }
        else
        {
            if (cell.ConnectTo.Index == selectedOrigin.Index)
            {
                if (previousCell?.Index != cell.Index && previousCell?.ConnectTo?.Index == selectedOrigin.Index && previousCell?.previousCell?.Index == cell.Index)
                {
                    selectedOrigin.DeleteCell(previousCell);
                }
                previousCell = cell;
            }

            else
            {
                // Replace
                if (selectedOrigin.Addable(cell, previousCell) && cell.ConnectTo.DeleteCell(cell))
                {
                    selectedOrigin.AddCell(cell, previousCell);
                    gameDef.OnCellManuallyAdded?.Invoke(this);
                    previousCell = cell;
                }
                else
                {
                    Unselect();
                    cell.ShowWrong();
                }
            }
        }
    }

    private DotController GetCell()
    {
        var mouseBoardPos = ctrl.GetMousePosition();
        var x = mouseBoardPos.x;
        var y = mouseBoardPos.y;
        return Cells.FirstOrDefault(n => !n.isBanned && Mathf.Abs(n.transform.localPosition.x - x) < dotDist / 2 && Mathf.Abs(n.transform.localPosition.y - y) < dotDist / 2);
    }

    public void PlaySound(Sounds sound)
    {
        if (Config.IS_CHECKING_SOLUTION) return;
        ctrl.PlaySound(sound);
    }

    private void CheckGameEnded()
    {
        if (Config.IS_CHECKING_SOLUTION) return;

        if (Cells.Any(v => !v.AllVehicleChecked()))
        {
            return;
        }

        gameDef.OnGameEnded?.Invoke(this);
        StartCoroutine(ShowWinAnimation());
        PlaySound(Sounds.Win);
    }

    private IEnumerator ShowWinAnimation()
    {
        var count = 0;
        yield return new WaitForSeconds(0.2f);

        // var vehicles = Cells
        //         .Where(c => c.cellType == DotType.Start)
        //         .SelectMany(c => c.startController.vehicles)
        //         .ToList();

        // foreach (var vehicle in vehicles.OrderBy(v => Guid.NewGuid()))
        // {
        //     yield return new WaitForSeconds(0.3f);
        //     vehicle.MovePath(() =>
        //     {
        //         count++;
        //         if (count == vehicles.Count)
        //         {
        //             PlaySound(Sounds.Next);
        //         }
        //     });
        // }
    }

    // [Button]
    void CheckSolution()
    {
        Config.IS_CHECKING_SOLUTION = true;
        var visitedMaps = new HashSet<string>();
        FindPath(Cells.Where(c => c.cellType == DotType.Start).ToList(), visitedMaps);
    }

    string HashMap()
    {
        return string.Join(",", Cells.Select(c => c.ConnectTo?.Index ?? -1));
    }

    void FindPath(List<DotController> occupiedCells, HashSet<string> visitedMaps)
    {
        var hash = HashMap();
        if (visitedMaps.Contains(hash))
        {
            return;
        }
        visitedMaps.Add(hash);

        if (visitedMaps.Count > 1000000)
        {
            Debug.Log($"Visited {visitedMaps.Count} maps");
            return;
        }

        if (Cells.All(v => v.AllVehicleChecked()))
        {
            Debug.Log("Found solution");
            var paths = Cells.Where(c => c.cellType == DotType.End).Select(c => c.GetPathToEnd()).ToList();

            var pathStrings = paths.Select(path => string.Join(",", path.Select(c => c.Index).ToArray()));

            Debug.Log($"{string.Join("; ", pathStrings)}");
            return;
        }

        if (occupiedCells.Count == Cells.Length)
        {
            return;
        }

        foreach (var cell in occupiedCells)
        {
            if (cell.ConnectTo.AllVehicleChecked())
            {
                continue;
            }

            var adjacentCells = Cells.Where(c => !c.isBanned && c.cellType != DotType.Start && CellOperations.IsAdjacentCell(cell, c));
            foreach (var adjacentCell in adjacentCells)
            {
                if (adjacentCell.ConnectTo == null && cell.ConnectTo.Addable(adjacentCell, cell))
                {
                    cell.ConnectTo.AddCell(adjacentCell, cell);
                    var newOccupiedCells = new List<DotController>(occupiedCells) { adjacentCell };
                    FindPath(newOccupiedCells, visitedMaps);
                    cell.ConnectTo.DeleteCell(adjacentCell);
                }
            }
        }
    }
}
