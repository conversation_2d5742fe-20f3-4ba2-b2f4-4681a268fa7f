using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class DotEndController : MonoBehaviour
{
    public DotController cell;
    public int symbolIndex = 0;
    public SpriteRenderer endRenderer;
    public GameObject symbolPrefab;
    public SymbolController symbolCtrl;

    // Start is called before the first frame update
    void Start()
    {
        symbolCtrl = Instantiate(symbolPrefab, transform).GetComponent<SymbolController>();
        symbolCtrl.shownInEnd = true;
        symbolCtrl.symbolIndex = symbolIndex;
        endRenderer.color = Config.DEFAULT_COLOR;
    }

    public void SetChecked(bool isChecked)
    {
        if (Config.IS_CHECKING_SOLUTION)
        {
            return;
        }

        if (isChecked)
        {
            endRenderer.color = Config.GetStartBgColor(cell.ConnectTo.startController.colorHue);
        }
        else
        {
            endRenderer.color = Config.DEFAULT_COLOR;
        }

        symbolCtrl.startDot = cell.ConnectTo;
        symbolCtrl.IsChecked = isChecked;
    }

    public void ShowWin(Action callback)
    {
        StartCoroutine(ShowPath(callback));
    }

    private List<DotController> GetPathToEnd()
    {
        var path = new List<DotController>();
        var cell = this.cell;
        while (cell != null)
        {
            path.Add(cell);
            cell = cell.previousCell;
        }
        path.Reverse();
        return path;
    }

    private IEnumerator ShowPath(Action callback)
    {
        var path = GetPathToEnd();
        foreach (var cell in path)
        {
            cell.ShowWinCircle(1f);
            yield return new WaitForSeconds(0.2f);
        }
        yield return new WaitForSeconds(0.8f);
        callback?.Invoke();
    }
}
