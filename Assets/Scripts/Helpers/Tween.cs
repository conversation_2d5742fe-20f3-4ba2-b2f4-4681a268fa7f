using System;
using System.Collections;
using UnityEngine;

public class Tween
{
    public event Action<Tween> OnStarted;
    public event Action<Tween> OnCompleted;

    public float Duration { get; set; }
    public float Position { get; set; } = 0;
    public float SpeedScale { get; set; } = 1;
    public float PauseOnStarted { get; set; } = 0f;
    public Func<float, float, float, float> EasingFunction { get; set; } = null;
    Action<float> _animate;

    bool _isPaused = false;
    bool _isForward = true;
    bool _isLooped = false;

    public Tween(float duration, Action<float> animate)
    {
        Duration = duration;
        _animate = animate;
    }

    public Tween(float duration, Func<float, float, float, float> easingFunction, Action<float> animate)
    {
        Duration = duration;
        _animate = animate;
        EasingFunction = easingFunction;
    }

    private IEnumerator Animate()
    {
        OnStarted?.Invoke(this);

        if (PauseOnStarted > 0 && !_isPaused)
        {
            yield return new WaitForSeconds(PauseOnStarted);
        }

        var elapsed = Duration * Position * SpeedScale;
        while (!_isPaused)
        {
            var value = elapsed / Duration;
            Position = EasingFunction == null ? value : EasingFunction.Invoke(0, 1, value);

            _animate(Position);
            yield return null;

            elapsed += _isForward ? Time.deltaTime : -Time.deltaTime;

            if ((_isForward && elapsed >= Duration) || (!_isForward && elapsed <= 0))
            {
                if (_isLooped)
                {
                    elapsed = _isForward ? 0 : Duration;
                }
                else
                {
                    break;
                }
            }
        }

        if (!_isPaused && ((_isForward && Position != 1) || (!_isForward && Position != 0)))
        {
            Position = _isForward ? 1 : 0;
            _animate(Position);
            yield return null;
        }

        if (!_isPaused)
        {
            OnCompleted?.Invoke(this);
        }

    }

    public IEnumerator Play()
    {
        _isForward = true;
        _isLooped = false;
        _isPaused = false;
        return Animate();
    }

    public void Pause()
    {
        _isPaused = true;
    }

    public IEnumerator Reverse()
    {
        _isForward = false;
        _isLooped = false;
        _isPaused = false;
        return Animate();
    }

    public IEnumerator Loop(bool forward = true)
    {
        _isForward = forward;
        _isLooped = true;
        _isPaused = false;
        return Animate();
    }
}