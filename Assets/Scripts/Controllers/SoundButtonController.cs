using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SoundButtonController : MonoBehaviour
{
    // Start is called before the first frame update
    public AudioSource soundSource;
    public GameObject soundButton;
    public GameObject soundMuteButton;

    // Start is called before the first frame update
    void Start()
    {
        var muted = GameRecord.GetIsSoundMuted();
        soundSource.mute = muted;
        soundButton.SetActive(!muted);
        soundMuteButton.SetActive(muted);
    }

    // Update is called once per frame
    public void OnClick()
    {
        var muted = !GameRecord.GetIsSoundMuted();
        GameRecord.SetMuted(muted);
        soundSource.mute = muted;
        soundButton.SetActive(!muted);
        soundMuteButton.SetActive(muted);
    }
}
