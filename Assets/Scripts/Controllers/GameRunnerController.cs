using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GameRunnerController : MonoBehaviour
{
    public RectTransform canvasRectTransform;
    public BoardController board;
    public SoundManagerController soundManager;

    // Start is called before the first frame update
    void Start()
    {

    }

    // Update is called once per frame
    void Update()
    {

    }

    public Vector2 GetMousePosition()
    {
        Vector2 vector;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(canvasRectTransform, Input.mousePosition, Camera.main, out vector);
        return vector;
    }

    public void PlaySound(Sounds sound)
    {
        soundManager.Play(sound);
    }
}
