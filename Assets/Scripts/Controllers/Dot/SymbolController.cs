using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;

public class SymbolController : MonoBehaviour
{
    private readonly Color LOW_LIGHT = new Color(0.2f, 0.2f, 0.2f, 1);
    private readonly Color HIGH_LIGHT = new Color(0.9f, 0.9f, 0.9f, 1);
    public bool shownInEnd = false;
    private bool _isChecked = false;
    private Tween showHighlightAnimation;
    private float startFontSize = 60;
    private float endFontSize = 240;


    void Awake()
    {
        var split = new TweenSplit(3, 7);
        showHighlightAnimation = new Tween(0.6f, (v) =>
        {
            var (value, index) = split.Split(v);
            switch (index)
            {
                case 0:
                    symbolText.fontSize = Mathf.Lerp(startFontSize, endFontSize, value);
                    break;
                case 1:
                    symbolText.fontSize = Mathf.Lerp(endFontSize, startFontSize, value);
                    break;
            }
        });
    }

    public bool IsChecked
    {
        get
        {
            return _isChecked;
        }
        set
        {
            _isChecked = value;
            symbolText.color = _isChecked ? GetHighLightColor() : GetLowLightColor();
            if (_isChecked)
            {
                StartCoroutine(showHighlightAnimation.Play());
            }
            else
            {
                showHighlightAnimation.Position = 0;
                showHighlightAnimation.Pause();
                symbolText.fontSize = startFontSize;
            }
        }
    }
    public int symbolIndex = 0;
    // public SpriteRenderer[] symbolRenderers;
    public TMP_Text symbolText;
    public DotController startDot;
    public DotController targetDot;

    private string[] symbols = new string[] { "+", "-", "#", ">" };


    // Start is called before the first frame update
    void Start()
    {
        // symbolRenderer = symbolRenderers[symbolIndex];
        symbolText.text = symbols[symbolIndex];
        // symbolText.gameObject.SetActive(true);
        symbolText.color = GetLowLightColor();
        // if (shownInEnd)
        // {
        //     symbolText.fontSize = 120;
        // }
    }

    private Color GetLowLightColor()
    {
        return LOW_LIGHT;
        // return startDot == null ? LOW_LIGHT : Color.HSVToRGB(startDot.startController.colorHue, 0.5f, 0.35f);
    }

    private Color GetHighLightColor()
    {
        return HIGH_LIGHT; // Color.HSVToRGB(startDot.startController.colorHue, 0.5f, 0.85f);)
    }
}
